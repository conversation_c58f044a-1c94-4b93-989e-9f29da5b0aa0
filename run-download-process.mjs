#!/usr/bin/env node

import { spawn } from 'child_process';
import fs from 'fs';

const TEMP_DIR = './temp';
const IMAGES_DIR = './images';

function runCommand(command, args = []) {
  return new Promise((resolve, reject) => {
    console.log(`🚀 Running: ${command} ${args.join(' ')}`);
    
    const process = spawn(command, args, {
      stdio: 'inherit',
      shell: true
    });

    process.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Command failed with exit code ${code}`));
      }
    });

    process.on('error', (error) => {
      reject(error);
    });
  });
}

async function checkPrerequisites() {
  console.log("🔍 Checking prerequisites...");
  
  // Check if star-gifts.json exists
  if (!fs.existsSync('./star-gifts.json')) {
    console.log("❌ star-gifts.json not found. Please run 'npm run get-collections' first.");
    process.exit(1);
  }
  
  console.log("✅ Prerequisites check passed");
}

async function downloadAndConvert() {
  try {
    await checkPrerequisites();
    
    console.log("\n📦 Starting complete download and conversion process...");
    console.log("=" .repeat(60));
    
    // Step 1: Download sticker files
    console.log("\n📥 Step 1: Downloading TGS sticker files from Telegram...");
    await runCommand('node', ['download-sticker-images.mjs']);
    
    // Check if any TGS files were downloaded
    if (!fs.existsSync(TEMP_DIR)) {
      console.log("❌ No TGS files were downloaded. Please check your Telegram authentication.");
      process.exit(1);
    }
    
    const tgsFiles = fs.readdirSync(TEMP_DIR).filter(file => file.endsWith('.tgs'));
    if (tgsFiles.length === 0) {
      console.log("❌ No TGS files found. Download may have failed.");
      process.exit(1);
    }
    
    console.log(`✅ Downloaded ${tgsFiles.length} TGS files`);
    
    // Step 2: Convert TGS files to images
    console.log("\n🎨 Step 2: Converting TGS files to images...");
    await runCommand('node', ['convert-tgs-to-images.mjs']);
    
    // Check results
    if (!fs.existsSync(IMAGES_DIR)) {
      console.log("❌ Images directory not created. Conversion may have failed.");
      process.exit(1);
    }
    
    const imageFiles = fs.readdirSync(IMAGES_DIR).filter(file => 
      file.endsWith('.svg') || file.endsWith('.png')
    );
    
    console.log("\n🎉 Process completed successfully!");
    console.log("=" .repeat(60));
    console.log(`📊 Results:`);
    console.log(`   📥 TGS files downloaded: ${tgsFiles.length}`);
    console.log(`   🖼️  Images generated: ${imageFiles.length}`);
    console.log(`   📁 TGS files location: ${TEMP_DIR}/`);
    console.log(`   📁 Images location: ${IMAGES_DIR}/`);
    
    // Show some example files
    if (imageFiles.length > 0) {
      console.log(`\n📋 Example generated files:`);
      imageFiles.slice(0, 5).forEach(file => {
        console.log(`   - ${file}`);
      });
      if (imageFiles.length > 5) {
        console.log(`   ... and ${imageFiles.length - 5} more`);
      }
    }
    
  } catch (error) {
    console.error("\n❌ Process failed:", error.message);
    process.exit(1);
  }
}

// Handle process interruption
process.on('SIGINT', () => {
  console.log('\n\n⚠️  Process interrupted by user');
  process.exit(1);
});

process.on('SIGTERM', () => {
  console.log('\n\n⚠️  Process terminated');
  process.exit(1);
});

console.log("🎬 Telegram Star Gifts - Complete Download Process");
console.log("This will download actual TGS sticker files and convert them to images");
console.log("You will need to authenticate with Telegram during this process.\n");

downloadAndConvert();
