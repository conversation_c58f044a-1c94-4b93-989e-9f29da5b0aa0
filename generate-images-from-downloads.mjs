import fs from "fs";
import path from "path";

const IMAGES_DIR = "./images";
const TEMP_DIR = "./temp";
const OUTPUT_DIR = "./generated-images";
const STAR_GIFTS_FILE = "./star-gifts.json";

// Ensure output directory exists
if (!fs.existsSync(OUTPUT_DIR)) {
  fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

function generateCollectionSVG(collectionData, hasDownloadedTgs = false) {
  const { collectionId, emoji, stars } = collectionData;
  
  // Get additional data from star-gifts.json if available
  let isLimited = false;
  let isSoldOut = false;
  let isBirthday = false;
  
  try {
    const starGiftsData = JSON.parse(fs.readFileSync(STAR_GIFTS_FILE, "utf8"));
    const giftData = starGiftsData.gifts.find(gift => gift.id.toString() === collectionId);
    if (giftData) {
      isLimited = giftData.limited || false;
      isSoldOut = giftData.soldOut || false;
      isBirthday = giftData.birthday || false;
    }
  } catch (error) {
    console.warn(`⚠️  Could not load additional data for collection ${collectionId}`);
  }

  // Determine background colors based on status
  let bgGradient;
  if (isBirthday) {
    bgGradient = `
      <defs>
        <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#FFE5B4;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#FFCCCB;stop-opacity:1" />
        </linearGradient>
      </defs>`;
  } else if (isLimited) {
    bgGradient = `
      <defs>
        <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#E6E6FA;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#DDA0DD;stop-opacity:1" />
        </linearGradient>
      </defs>`;
  } else if (isSoldOut) {
    bgGradient = `
      <defs>
        <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#D3D3D3;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#A9A9A9;stop-opacity:1" />
        </linearGradient>
      </defs>`;
  } else {
    bgGradient = `
      <defs>
        <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#E0F6FF;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#87CEEB;stop-opacity:1" />
        </linearGradient>
      </defs>`;
  }

  // Status badges
  let statusBadges = "";
  let badgeY = 120;
  
  if (isBirthday) {
    statusBadges += `
      <circle cx="400" cy="${badgeY}" r="25" fill="#FF69B4" stroke="#FFF" stroke-width="2"/>
      <text x="400" y="${badgeY + 5}" text-anchor="middle" font-size="20" fill="#FFF">🎂</text>`;
    badgeY += 60;
  }
  
  if (isLimited) {
    statusBadges += `
      <circle cx="400" cy="${badgeY}" r="25" fill="#9370DB" stroke="#FFF" stroke-width="2"/>
      <text x="400" y="${badgeY + 5}" text-anchor="middle" font-size="20" fill="#FFF">⚡</text>`;
    badgeY += 60;
  }
  
  if (isSoldOut) {
    statusBadges += `
      <circle cx="400" cy="${badgeY}" r="25" fill="#FF6347" stroke="#FFF" stroke-width="2"/>
      <text x="400" y="${badgeY + 5}" text-anchor="middle" font-size="20" fill="#FFF">❌</text>`;
  }

  // Downloaded indicator
  const downloadedIndicator = hasDownloadedTgs ? `
    <rect x="50" y="250" width="412" height="40" fill="rgba(0, 255, 0, 0.2)" stroke="#00FF00" stroke-width="2" rx="20"/>
    <text x="256" y="275" text-anchor="middle" font-size="16" fill="#006400" font-weight="bold">✅ TGS Downloaded</text>
  ` : `
    <rect x="50" y="250" width="412" height="40" fill="rgba(255, 165, 0, 0.2)" stroke="#FFA500" stroke-width="2" rx="20"/>
    <text x="256" y="275" text-anchor="middle" font-size="16" fill="#FF8C00" font-weight="bold">📋 Marker Only</text>
  `;

  const svg = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" xmlns="http://www.w3.org/2000/svg">
  ${bgGradient}
  
  <!-- Background -->
  <rect width="512" height="512" fill="url(#bg)"/>
  
  <!-- Border -->
  <rect x="2" y="2" width="508" height="508" fill="none" stroke="#333" stroke-width="4"/>
  
  <!-- Main emoji -->
  <text x="256" y="200" text-anchor="middle" font-size="120" font-family="Arial, sans-serif">${emoji}</text>
  
  <!-- Downloaded/Status indicator -->
  ${downloadedIndicator}
  
  <!-- Stars badge -->
  <circle cx="400" cy="60" r="40" fill="#FFD700" stroke="#FFA500" stroke-width="3"/>
  <text x="400" y="50" text-anchor="middle" font-size="24" fill="#FFF">⭐</text>
  <text x="400" y="72" text-anchor="middle" font-size="16" fill="#000" font-weight="bold">${stars}</text>
  
  <!-- Status badges -->
  ${statusBadges}
  
  <!-- Collection ID background -->
  <rect x="0" y="420" width="512" height="92" fill="rgba(0, 0, 0, 0.8)"/>
  
  <!-- Collection ID text -->
  <text x="256" y="450" text-anchor="middle" font-size="24" fill="#FFF" font-family="Arial, sans-serif">Collection ID</text>
  <text x="256" y="480" text-anchor="middle" font-size="20" fill="#FFF" font-family="monospace">${collectionId}</text>
</svg>`;

  return svg;
}

function generateImagesFromDownloads() {
  try {
    console.log("🔍 Scanning downloaded collections...");
    
    // Get all marker files
    const markerFiles = fs.readdirSync(IMAGES_DIR)
      .filter(file => file.endsWith('.downloaded'));
    
    console.log(`📋 Found ${markerFiles.length} downloaded collections`);
    
    let processedCount = 0;
    let skippedCount = 0;
    let withTgsCount = 0;
    let markerOnlyCount = 0;

    markerFiles.forEach((markerFile, index) => {
      try {
        // Read marker data
        const markerPath = path.join(IMAGES_DIR, markerFile);
        const markerData = JSON.parse(fs.readFileSync(markerPath, "utf8"));
        
        const { collectionId, emoji, stars, tgsFile } = markerData;
        
        // Check if corresponding TGS file exists
        const tgsPath = path.join(TEMP_DIR, tgsFile);
        const hasTgsFile = fs.existsSync(tgsPath);
        
        if (hasTgsFile) {
          withTgsCount++;
        } else {
          markerOnlyCount++;
        }
        
        // Check if SVG already exists
        const svgFilename = `${collectionId}.svg`;
        const svgPath = path.join(OUTPUT_DIR, svgFilename);
        
        if (fs.existsSync(svgPath)) {
          console.log(`⏭️  Skipping collection ${collectionId} - SVG already exists`);
          skippedCount++;
          return;
        }
        
        // Generate SVG
        const svg = generateCollectionSVG(markerData, hasTgsFile);
        
        // Save SVG file
        fs.writeFileSync(svgPath, svg, "utf8");
        
        processedCount++;
        
        if ((index + 1) % 10 === 0) {
          console.log(`✅ Processed ${index + 1}/${markerFiles.length} collections...`);
        }
        
      } catch (error) {
        console.error(`❌ Error processing collection ${markerFile}:`, error.message);
        skippedCount++;
      }
    });

    console.log("\n🎉 Image generation completed!");
    console.log(`📊 Results:`);
    console.log(`   ✅ Successfully processed: ${processedCount} images`);
    console.log(`   ⏭️  Skipped (already exist): ${skippedCount} images`);
    console.log(`   📁 With TGS files: ${withTgsCount} collections`);
    console.log(`   📋 Marker only: ${markerOnlyCount} collections`);
    console.log(`📁 SVG files saved to: ${OUTPUT_DIR}`);

    // Generate summary file
    const summaryData = {
      timestamp: new Date().toISOString(),
      totalMarkers: markerFiles.length,
      processedCount,
      skippedCount,
      withTgsCount,
      markerOnlyCount,
      outputDirectory: OUTPUT_DIR,
      sourceDirectories: {
        markers: IMAGES_DIR,
        tgsFiles: TEMP_DIR
      }
    };

    fs.writeFileSync(
      path.join(OUTPUT_DIR, "generation-summary.json"),
      JSON.stringify(summaryData, null, 2)
    );

    console.log(`📋 Generation summary saved to: ${path.join(OUTPUT_DIR, "generation-summary.json")}`);

  } catch (error) {
    console.error("❌ Error generating images:", error.message);
    process.exit(1);
  }
}

console.log("🎨 Starting image generation from downloads...");
generateImagesFromDownloads();
