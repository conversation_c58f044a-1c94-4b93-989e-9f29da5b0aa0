import { TelegramClient, Api } from "telegram";
import { StringSession } from "telegram/sessions/index.js";
import readline from "readline";
import fs from "fs";

const apiId = 14431416;
const apiHash = "da8fa0a17dd9e0c1b9e420d73a39a710";

const stringSession = new StringSession("");

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

(async () => {
  const client = new TelegramClient(stringSession, apiId, apiHash, {
    connectionRetries: 5,
  });

  // Authenticate
  await client.start({
    phoneNumber: () =>
      new Promise((resolve) => rl.question("Enter phone number: ", resolve)),
    phoneCode: () =>
      new Promise((resolve) => rl.question("Enter code: ", resolve)),
    password: () =>
      new Promise((resolve) =>
        rl.question("Enter password (if 2FA): ", resolve)
      ),
    onError: (err) => console.log("Error:", err),
  });

  console.log("Connected. Session:", client.session.save());

  try {
    // Fetch Star Gifts
    const result = await client.invoke(
      new Api.payments.GetStarGifts({ hash: 0 })
    );

    console.log(`\n📊 Total Star Gifts Found: ${result.gifts.length}\n`);

    // Save complete gifts data as JSON
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const jsonFilename = `star-gifts-${timestamp}.json`;

    try {
      const jsonData = {
        timestamp: new Date().toISOString(),
        totalGifts: result.gifts.length,
        gifts: result.gifts,
      };

      fs.writeFileSync(jsonFilename, JSON.stringify(jsonData, null, 2));
      console.log(`💾 Complete gifts data saved to: ${jsonFilename}`);
    } catch (jsonError) {
      console.error("❌ Error saving JSON file:", jsonError.message);
    }

    // Filter unrevealed collections (not sold out, not limited, has stock)
    const unrevealedGifts = result.gifts.filter(
      (gift) => !gift.soldOut && !gift.limited && gift.availabilityRemains > 0
    );

    console.log("🎁 UNREVEALED COLLECTIONS (Available for Purchase):");
    console.log("=".repeat(60));

    if (unrevealedGifts.length === 0) {
      console.log("❌ No unrevealed collections found.");
    } else {
      const collectionIds = [];

      unrevealedGifts.forEach((gift, index) => {
        const giftId = gift.id.value || gift.id;
        const stars = gift.stars.value || gift.stars;

        collectionIds.push(giftId.toString());

        console.log(`\n${index + 1}. 🌟 Collection ID: ${giftId}`);
        console.log(`   💰 Price: ${stars} stars`);
        console.log(
          `   📦 Stock: ${gift.availabilityRemains}/${gift.availabilityTotal}`
        );
        console.log(`   🎂 Birthday Gift: ${gift.birthday ? "Yes" : "No"}`);

        if (gift.firstSaleDate) {
          const firstSale = new Date(gift.firstSaleDate * 1000);
          console.log(`   📅 First Sale: ${firstSale.toLocaleDateString()}`);
        }

        if (gift.lastSaleDate) {
          const lastSale = new Date(gift.lastSaleDate * 1000);
          console.log(`   📅 Last Sale: ${lastSale.toLocaleDateString()}`);
        }
      });

      // Save collection IDs to text file
      const textTimestamp = new Date().toISOString().replace(/[:.]/g, "-");
      const filename = `unrevealed-collections-${textTimestamp}.txt`;

      const fileContent = [
        `Unrevealed Star Gift Collection IDs`,
        `Generated: ${new Date().toLocaleString()}`,
        `Total Collections: ${collectionIds.length}`,
        ``,
        `Collection IDs:`,
        ...collectionIds.map((id, index) => `${index + 1}. ${id}`),
      ].join("\n");

      fs.writeFileSync(filename, fileContent);

      console.log(`\n💾 Collection IDs saved to: ${filename}`);
      console.log(`📋 Total unrevealed collections: ${collectionIds.length}`);
    }
  } catch (error) {
    console.error("❌ Error fetching gifts:", error.message);
    if (error.stack) {
      console.error("Stack trace:", error.stack);
    }
  }

  // Cleanup
  await client.disconnect();
  rl.close();
})();
