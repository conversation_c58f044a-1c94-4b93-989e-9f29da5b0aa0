const fs = require("fs");
const path = require("path");
const { createCanvas } = require("canvas");

const INPUT_FILE = "./star-gifts-cleaned.json";
const IMAGES_DIR = "./images";

// Ensure images directory exists
if (!fs.existsSync(IMAGES_DIR)) {
  fs.mkdirSync(IMAGES_DIR, { recursive: true });
}

function generateCollectionImage(collectionId, emoji, stars, isLimited, isSoldOut, isBirthday) {
  // Create canvas (512x512 to match sticker dimensions)
  const canvas = createCanvas(512, 512);
  const ctx = canvas.getContext("2d");

  // Background gradient
  const gradient = ctx.createLinearGradient(0, 0, 512, 512);
  if (isBirthday) {
    gradient.addColorStop(0, "#FFE5B4"); // Peach
    gradient.addColorStop(1, "#FFCCCB"); // Light pink
  } else if (isLimited) {
    gradient.addColorStop(0, "#E6E6FA"); // Lavender
    gradient.addColorStop(1, "#DDA0DD"); // Plum
  } else if (isSoldOut) {
    gradient.addColorStop(0, "#D3D3D3"); // Light gray
    gradient.addColorStop(1, "#A9A9A9"); // Dark gray
  } else {
    gradient.addColorStop(0, "#E0F6FF"); // Light blue
    gradient.addColorStop(1, "#87CEEB"); // Sky blue
  }
  
  ctx.fillStyle = gradient;
  ctx.fillRect(0, 0, 512, 512);

  // Border
  ctx.strokeStyle = "#333";
  ctx.lineWidth = 4;
  ctx.strokeRect(2, 2, 508, 508);

  // Main emoji (large, centered)
  ctx.font = "200px Arial";
  ctx.textAlign = "center";
  ctx.textBaseline = "middle";
  ctx.fillStyle = "#000";
  ctx.fillText(emoji, 256, 220);

  // Stars badge (top right)
  const badgeX = 400;
  const badgeY = 60;
  const badgeRadius = 40;
  
  ctx.fillStyle = "#FFD700";
  ctx.beginPath();
  ctx.arc(badgeX, badgeY, badgeRadius, 0, 2 * Math.PI);
  ctx.fill();
  ctx.strokeStyle = "#FFA500";
  ctx.lineWidth = 3;
  ctx.stroke();

  // Star symbol in badge
  ctx.fillStyle = "#FFF";
  ctx.font = "24px Arial";
  ctx.fillText("⭐", badgeX, badgeY - 8);
  
  // Stars count
  ctx.fillStyle = "#000";
  ctx.font = "16px Arial";
  ctx.fillText(stars, badgeX, badgeY + 12);

  // Status badges
  let statusY = 120;
  if (isBirthday) {
    drawStatusBadge(ctx, 400, statusY, "🎂", "#FF69B4");
    statusY += 60;
  }
  if (isLimited) {
    drawStatusBadge(ctx, 400, statusY, "⚡", "#9370DB");
    statusY += 60;
  }
  if (isSoldOut) {
    drawStatusBadge(ctx, 400, statusY, "❌", "#FF6347");
    statusY += 60;
  }

  // Collection ID (bottom)
  ctx.fillStyle = "rgba(0, 0, 0, 0.8)";
  ctx.fillRect(0, 420, 512, 92);
  
  ctx.fillStyle = "#FFF";
  ctx.font = "24px Arial";
  ctx.textAlign = "center";
  ctx.fillText("Collection ID", 256, 450);
  
  ctx.font = "20px monospace";
  ctx.fillText(collectionId, 256, 480);

  return canvas;
}

function drawStatusBadge(ctx, x, y, emoji, color) {
  const radius = 25;
  
  ctx.fillStyle = color;
  ctx.beginPath();
  ctx.arc(x, y, radius, 0, 2 * Math.PI);
  ctx.fill();
  
  ctx.strokeStyle = "#FFF";
  ctx.lineWidth = 2;
  ctx.stroke();
  
  ctx.fillStyle = "#FFF";
  ctx.font = "20px Arial";
  ctx.textAlign = "center";
  ctx.textBaseline = "middle";
  ctx.fillText(emoji, x, y);
}

function generateCollectionImages() {
  try {
    console.log("📖 Reading gifts data...");
    const rawData = fs.readFileSync(INPUT_FILE, "utf8");
    const data = JSON.parse(rawData);

    console.log(`🎁 Processing ${data.gifts.length} gifts...`);

    let processedCount = 0;
    let skippedCount = 0;

    data.gifts.forEach((gift, index) => {
      try {
        const collectionId = gift.id.toString();
        const stars = gift.stars.toString();
        const isLimited = gift.limited || false;
        const isSoldOut = gift.soldOut || false;
        const isBirthday = gift.birthday || false;

        // Extract emoji from sticker attributes
        let emoji = "🎁"; // Default emoji
        if (gift.sticker && gift.sticker.attributes) {
          const customEmojiAttr = gift.sticker.attributes.find(
            attr => attr.className === "DocumentAttributeCustomEmoji"
          );
          if (customEmojiAttr && customEmojiAttr.alt) {
            emoji = customEmojiAttr.alt;
          }
        }

        // Generate image
        const canvas = generateCollectionImage(
          collectionId,
          emoji,
          stars,
          isLimited,
          isSoldOut,
          isBirthday
        );

        // Save image
        const filename = `${collectionId}.png`;
        const filepath = path.join(IMAGES_DIR, filename);
        
        const buffer = canvas.toBuffer("image/png");
        fs.writeFileSync(filepath, buffer);

        processedCount++;

        if ((index + 1) % 10 === 0) {
          console.log(`✅ Processed ${index + 1}/${data.gifts.length} gifts...`);
        }

      } catch (error) {
        console.error(`❌ Error processing gift ${index + 1}:`, error.message);
        skippedCount++;
      }
    });

    console.log("\n🎉 Image generation completed!");
    console.log(`📊 Results:`);
    console.log(`   ✅ Successfully processed: ${processedCount} images`);
    console.log(`   ❌ Skipped due to errors: ${skippedCount} images`);
    console.log(`📁 Images saved to: ${IMAGES_DIR}`);

  } catch (error) {
    console.error("❌ Error reading gifts data:", error.message);
    process.exit(1);
  }
}

// Check if canvas package is available
try {
  require("canvas");
  console.log("🎨 Starting collection image generation...");
  generateCollectionImages();
} catch (error) {
  console.error("❌ Canvas package not found. Please install it first:");
  console.error("npm install canvas");
  process.exit(1);
}
