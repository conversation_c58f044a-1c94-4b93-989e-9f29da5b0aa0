# Telegram Star Gifts Collection Image Generator

This directory contains tools for processing Telegram Star Gifts data and generating visual representations of each collection.

## Files Overview

- `get-collections.mjs` - Fetches Star Gifts data from Telegram API
- `clean-star-gifts.js` - Cleans the raw gifts data by removing unnecessary binary data
- `download-sticker-images.mjs` - Downloads actual TGS sticker files from Telegram
- `convert-tgs-to-images.mjs` - Converts downloaded TGS files to images
- `generate-collection-images-svg.js` - Generates SVG images (fallback option)
- `star-gifts.json` - Raw gifts data from Telegram API (with fileReference data)
- `star-gifts-cleaned.json` - Cleaned gifts data (fileReference removed)
- `images/` - Directory where generated images are saved
- `temp/` - Directory where downloaded TGS files are stored

## Setup

1. Install dependencies:

```bash
npm install
```

2. For PNG generation (optional):

```bash
npm install canvas
```

## Usage

### Generate Collection Images

#### Option 1: Download Actual Sticker Files (Recommended)

```bash
# Download TGS sticker files from Telegram
npm run download-stickers

# Convert TGS files to images
npm run convert-tgs
```

#### Option 2: Generate SVG Placeholders (No Telegram auth required)

```bash
npm run generate-svg
```

### Complete Processing Pipeline

```bash
# Download actual sticker files and convert to images
npm run download-all
```

This will:

1. Authenticate with Telegram
2. Download actual TGS sticker files using fileReference data
3. Convert TGS files to enhanced SVG/PNG images with actual sticker data

## Generated Images

Each image includes:

- **Collection emoji** - Large centered emoji representing the gift
- **Stars badge** - Shows the cost in stars
- **Status badges** - Visual indicators for:
  - 🎂 Birthday gifts
  - ⚡ Limited editions
  - ❌ Sold out items
- **Collection ID** - Unique identifier at the bottom
- **Color-coded background** - Different gradients based on gift type

### Image Naming

Images are saved with the collection ID as the filename:

- `5170145012310081615.svg`
- `5170233102089322756.svg`
- etc.

### Background Colors

- **Birthday gifts**: Peach to light pink gradient
- **Limited editions**: Lavender to plum gradient
- **Sold out**: Light gray to dark gray gradient
- **Regular gifts**: Light blue to sky blue gradient

## File Structure

```
gifts/
├── images/                          # Generated images directory
│   ├── 5170145012310081615.svg     # Collection image files
│   ├── 5170233102089322756.svg
│   └── generation-summary.json      # Generation statistics
├── get-collections.mjs             # Telegram API fetcher
├── clean-star-gifts.js             # Data cleaner
├── generate-collection-images.js   # PNG generator
├── generate-collection-images-svg.js # SVG generator
├── star-gifts.json                 # Raw data
├── star-gifts-cleaned.json         # Cleaned data
├── package.json                    # Dependencies
└── README.md                       # This file
```

## Image Formats

### SVG Format (Recommended)

- Vector graphics - scalable to any size
- Small file size
- No external dependencies
- Perfect for web display

### PNG Format (Optional)

- Raster graphics - fixed resolution (512x512)
- Larger file size
- Requires canvas package
- Good for applications requiring bitmap images

## Troubleshooting

### Canvas Installation Issues

If you encounter issues installing the canvas package:

**macOS:**

```bash
brew install pkg-config cairo pango libpng jpeg giflib librsvg
```

**Ubuntu/Debian:**

```bash
sudo apt-get install build-essential libcairo2-dev libpango1.0-dev libjpeg-dev libgif-dev librsvg2-dev
```

**Windows:**

- Install Visual Studio Build Tools
- Install Python 2.7
- Use `npm install canvas --build-from-source`

### Alternative: Use SVG Only

If canvas installation fails, use the SVG generator instead:

```bash
npm run generate-svg
```

## Output

After running the image generation, you'll see:

- Individual image files in the `images/` directory
- A `generation-summary.json` file with statistics
- Console output showing progress and results
