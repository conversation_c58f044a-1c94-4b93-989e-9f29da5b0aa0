import fs from "fs";
import path from "path";
import { spawn } from "child_process";
import puppeteer from "puppeteer";

const TEMP_DIR = "./temp";
const IMAGES_DIR = "./images";
const INPUT_FILE = "./star-gifts.json";

// Ensure directories exist
[IMAGES_DIR].forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

async function convertTgsToImage(tgsFilePath, outputImagePath, collectionId, emoji, stars, isLimited, isSoldOut, isBirthday) {
  try {
    console.log(`🎨 Converting TGS to image for collection ${collectionId}...`);

    // Read the TGS file (it's a gzipped JSON)
    const tgsBuffer = fs.readFileSync(tgsFilePath);
    
    // For now, create an enhanced SVG with the actual TGS file info
    const svg = await generateEnhancedSVG(
      collectionId, 
      emoji, 
      stars, 
      isLimited, 
      isSoldOut, 
      isBirthday, 
      tgsBuffer.length
    );
    
    const svgPath = outputImagePath.replace('.png', '.svg');
    fs.writeFileSync(svgPath, svg);
    
    // Optionally convert SVG to PNG using puppeteer
    if (outputImagePath.endsWith('.png')) {
      await convertSvgToPng(svgPath, outputImagePath);
    }
    
    console.log(`✅ Created image for collection ${collectionId}`);
    return svgPath;

  } catch (error) {
    console.error(`❌ Error converting TGS for collection ${collectionId}:`, error.message);
    return null;
  }
}

async function convertSvgToPng(svgPath, pngPath) {
  try {
    const browser = await puppeteer.launch({ headless: true });
    const page = await browser.newPage();
    
    // Set viewport size
    await page.setViewport({ width: 512, height: 512 });
    
    // Read SVG content
    const svgContent = fs.readFileSync(svgPath, 'utf8');
    
    // Create HTML with SVG
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <style>
            body { margin: 0; padding: 0; }
            svg { display: block; }
          </style>
        </head>
        <body>
          ${svgContent}
        </body>
      </html>
    `;
    
    await page.setContent(html);
    
    // Take screenshot
    await page.screenshot({
      path: pngPath,
      type: 'png',
      clip: { x: 0, y: 0, width: 512, height: 512 }
    });
    
    await browser.close();
    console.log(`🖼️  Converted SVG to PNG: ${path.basename(pngPath)}`);
    
  } catch (error) {
    console.error(`❌ Error converting SVG to PNG:`, error.message);
  }
}

async function generateEnhancedSVG(collectionId, emoji, stars, isLimited, isSoldOut, isBirthday, tgsFileSize) {
  // Determine background colors based on status
  let bgGradient;
  if (isBirthday) {
    bgGradient = `
      <defs>
        <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#FFE5B4;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#FFCCCB;stop-opacity:1" />
        </linearGradient>
      </defs>`;
  } else if (isLimited) {
    bgGradient = `
      <defs>
        <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#E6E6FA;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#DDA0DD;stop-opacity:1" />
        </linearGradient>
      </defs>`;
  } else if (isSoldOut) {
    bgGradient = `
      <defs>
        <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#D3D3D3;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#A9A9A9;stop-opacity:1" />
        </linearGradient>
      </defs>`;
  } else {
    bgGradient = `
      <defs>
        <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#E0F6FF;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#87CEEB;stop-opacity:1" />
        </linearGradient>
      </defs>`;
  }

  // Status badges
  let statusBadges = "";
  let badgeY = 120;
  
  if (isBirthday) {
    statusBadges += `
      <circle cx="400" cy="${badgeY}" r="25" fill="#FF69B4" stroke="#FFF" stroke-width="2"/>
      <text x="400" y="${badgeY + 5}" text-anchor="middle" font-size="20" fill="#FFF">🎂</text>`;
    badgeY += 60;
  }
  
  if (isLimited) {
    statusBadges += `
      <circle cx="400" cy="${badgeY}" r="25" fill="#9370DB" stroke="#FFF" stroke-width="2"/>
      <text x="400" y="${badgeY + 5}" text-anchor="middle" font-size="20" fill="#FFF">⚡</text>`;
    badgeY += 60;
  }
  
  if (isSoldOut) {
    statusBadges += `
      <circle cx="400" cy="${badgeY}" r="25" fill="#FF6347" stroke="#FFF" stroke-width="2"/>
      <text x="400" y="${badgeY + 5}" text-anchor="middle" font-size="20" fill="#FFF">❌</text>`;
  }

  // Format file size
  const fileSizeKB = (tgsFileSize / 1024).toFixed(1);

  const svg = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" xmlns="http://www.w3.org/2000/svg">
  ${bgGradient}
  
  <!-- Background -->
  <rect width="512" height="512" fill="url(#bg)"/>
  
  <!-- Border -->
  <rect x="2" y="2" width="508" height="508" fill="none" stroke="#333" stroke-width="4"/>
  
  <!-- Main emoji -->
  <text x="256" y="180" text-anchor="middle" font-size="120" font-family="Arial, sans-serif">${emoji}</text>
  
  <!-- TGS Downloaded indicator -->
  <rect x="50" y="220" width="412" height="35" fill="rgba(0, 255, 0, 0.2)" stroke="#00FF00" stroke-width="2" rx="17"/>
  <text x="256" y="242" text-anchor="middle" font-size="14" fill="#006400" font-weight="bold">TGS Downloaded (${fileSizeKB} KB)</text>
  
  <!-- Animated sticker indicator -->
  <rect x="80" y="265" width="352" height="30" fill="rgba(255, 165, 0, 0.2)" stroke="#FF8C00" stroke-width="2" rx="15"/>
  <text x="256" y="283" text-anchor="middle" font-size="12" fill="#FF8C00" font-weight="bold">🎬 Animated Sticker File</text>
  
  <!-- Stars badge -->
  <circle cx="400" cy="60" r="40" fill="#FFD700" stroke="#FFA500" stroke-width="3"/>
  <text x="400" y="50" text-anchor="middle" font-size="24" fill="#FFF">⭐</text>
  <text x="400" y="72" text-anchor="middle" font-size="16" fill="#000" font-weight="bold">${stars}</text>
  
  <!-- Status badges -->
  ${statusBadges}
  
  <!-- Collection ID background -->
  <rect x="0" y="420" width="512" height="92" fill="rgba(0, 0, 0, 0.8)"/>
  
  <!-- Collection ID text -->
  <text x="256" y="450" text-anchor="middle" font-size="24" fill="#FFF" font-family="Arial, sans-serif">Collection ID</text>
  <text x="256" y="480" text-anchor="middle" font-size="20" fill="#FFF" font-family="monospace">${collectionId}</text>
</svg>`;

  return svg;
}

async function processExistingTgsFiles() {
  try {
    // Read gifts data for metadata
    console.log("📖 Reading gifts data...");
    const rawData = fs.readFileSync(INPUT_FILE, "utf8");
    const data = JSON.parse(rawData);

    // Create a lookup map for gift metadata
    const giftMap = new Map();
    data.gifts.forEach(gift => {
      const collectionId = gift.id.toString();
      const stars = gift.stars.toString();
      const isLimited = gift.limited || false;
      const isSoldOut = gift.soldOut || false;
      const isBirthday = gift.birthday || false;

      let emoji = "🎁";
      if (gift.sticker && gift.sticker.attributes) {
        const customEmojiAttr = gift.sticker.attributes.find(
          attr => attr.className === "DocumentAttributeCustomEmoji"
        );
        if (customEmojiAttr && customEmojiAttr.alt) {
          emoji = customEmojiAttr.alt;
        }
      }

      giftMap.set(collectionId, {
        emoji,
        stars,
        isLimited,
        isSoldOut,
        isBirthday
      });
    });

    // Check if temp directory exists
    if (!fs.existsSync(TEMP_DIR)) {
      console.log("❌ Temp directory not found. Please run download-stickers first.");
      return;
    }

    // Get all TGS files
    const tgsFiles = fs.readdirSync(TEMP_DIR).filter(file => file.endsWith('.tgs'));
    
    if (tgsFiles.length === 0) {
      console.log("❌ No TGS files found in temp directory. Please run download-stickers first.");
      return;
    }

    console.log(`🎨 Converting ${tgsFiles.length} TGS files to images...`);

    let processedCount = 0;
    let skippedCount = 0;

    for (const tgsFile of tgsFiles) {
      try {
        const collectionId = path.basename(tgsFile, '.tgs');
        const giftData = giftMap.get(collectionId);

        if (!giftData) {
          console.log(`⚠️  No metadata found for collection ${collectionId}`);
          skippedCount++;
          continue;
        }

        const tgsPath = path.join(TEMP_DIR, tgsFile);
        const imagePath = path.join(IMAGES_DIR, `${collectionId}.png`);

        // Check if image already exists
        if (fs.existsSync(imagePath) || fs.existsSync(imagePath.replace('.png', '.svg'))) {
          console.log(`⏭️  Skipping collection ${collectionId} - image already exists`);
          processedCount++;
          continue;
        }

        await convertTgsToImage(
          tgsPath,
          imagePath,
          collectionId,
          giftData.emoji,
          giftData.stars,
          giftData.isLimited,
          giftData.isSoldOut,
          giftData.isBirthday
        );

        processedCount++;

        // Progress update
        if (processedCount % 10 === 0) {
          console.log(`📊 Progress: ${processedCount}/${tgsFiles.length} files converted...`);
        }

      } catch (error) {
        console.error(`❌ Error processing ${tgsFile}:`, error.message);
        skippedCount++;
      }
    }

    console.log("\n🎉 TGS conversion completed!");
    console.log(`📊 Results:`);
    console.log(`   ✅ Successfully converted: ${processedCount} files`);
    console.log(`   ❌ Skipped due to errors: ${skippedCount} files`);
    console.log(`📁 Images saved to: ${IMAGES_DIR}`);

  } catch (error) {
    console.error("❌ Error in conversion process:", error.message);
  }
}

console.log("🎬 Starting TGS to image conversion...");
processExistingTgsFiles();
