# Image Generation from Downloads

This document explains how to generate collection images based on downloaded TGS files and marker data.

## Overview

The `generate-images-from-downloads.mjs` script creates SVG images for Telegram Star Gift collections that have been successfully downloaded. It reads from two source directories:

- `./images/` - Contains `.downloaded` marker files with collection metadata
- `./temp/` - Contains `.tgs` sticker files downloaded from Telegram

## Features

✅ **Smart Processing**: Only processes collections that have been successfully downloaded  
✅ **TGS Detection**: Automatically detects which collections have actual TGS files vs. marker-only  
✅ **Status Indicators**: Visual indicators showing download status  
✅ **Skip Existing**: Avoids regenerating already existing SVG files  
✅ **Rich Metadata**: Includes collection ID, emoji, stars, and status badges  
✅ **Progress Tracking**: Shows progress during generation  
✅ **Summary Report**: Generates detailed summary of the process  

## Usage

### Quick Start

```bash
# Generate images from downloaded collections
npm run generate-from-downloads
```

### Manual Execution

```bash
node generate-images-from-downloads.mjs
```

## Input Structure

### Marker Files (`./images/*.downloaded`)
```json
{
  "collectionId": "5170145012310081615",
  "emoji": "💝",
  "stars": "15",
  "downloadedAt": "2025-06-19T20:52:40.954Z",
  "tgsFile": "5170145012310081615.tgs"
}
```

### TGS Files (`./temp/*.tgs`)
Binary Telegram sticker files downloaded from the API.

## Output

### Generated Images (`./generated-images/`)
- **Format**: SVG files (512x512px)
- **Naming**: `{collectionId}.svg`
- **Features**:
  - Collection emoji (large, centered)
  - Star count badge (top-right)
  - Download status indicator (green for TGS downloaded, orange for marker-only)
  - Status badges (limited, sold out, birthday)
  - Collection ID (bottom)
  - Gradient backgrounds based on collection status

### Summary Report (`./generated-images/generation-summary.json`)
```json
{
  "timestamp": "2025-06-19T20:56:04.077Z",
  "totalMarkers": 121,
  "processedCount": 121,
  "skippedCount": 0,
  "withTgsCount": 121,
  "markerOnlyCount": 0,
  "outputDirectory": "./generated-images",
  "sourceDirectories": {
    "markers": "./images",
    "tgsFiles": "./temp"
  }
}
```

## Visual Indicators

### Download Status
- **✅ TGS Downloaded** (Green): Collection has both marker and TGS file
- **📋 Marker Only** (Orange): Collection has marker but no TGS file

### Collection Status Badges
- **🎂 Birthday** (Pink): Special birthday collections
- **⚡ Limited** (Purple): Limited edition collections  
- **❌ Sold Out** (Red): Collections that are sold out

### Background Colors
- **Blue Gradient**: Regular collections
- **Purple Gradient**: Limited collections
- **Gray Gradient**: Sold out collections
- **Pink/Orange Gradient**: Birthday collections

## Dependencies

The script uses the original `star-gifts.json` file to fetch additional metadata (limited, soldOut, birthday status) for enhanced visual representation.

## Error Handling

- **Missing TGS Files**: Collections without TGS files still generate images with "Marker Only" indicator
- **Missing Metadata**: Collections without additional data use default styling
- **Existing Files**: Skips regeneration of existing SVG files
- **Invalid Markers**: Logs errors and continues processing other collections

## Performance

- **Fast Processing**: Processes 121 collections in seconds
- **Memory Efficient**: Processes files one at a time
- **Progress Updates**: Shows progress every 10 collections
- **Incremental**: Only processes new collections on subsequent runs

## Integration

This script is designed to work with the download workflow:

1. **Download**: `npm run download-stickers` - Downloads TGS files and creates markers
2. **Generate**: `npm run generate-from-downloads` - Creates SVG images from downloads
3. **Result**: High-quality SVG images for all successfully downloaded collections

## Troubleshooting

### No Images Generated
- Check that `./images/` contains `.downloaded` files
- Verify the script has write permissions to `./generated-images/`

### Missing Status Information
- Ensure `star-gifts.json` exists in the project root
- Check that collection IDs match between marker files and star-gifts.json

### Performance Issues
- Large numbers of collections are processed efficiently
- Consider running in smaller batches if memory is limited
