{"name": "telegram-star-gifts-processor", "version": "1.0.0", "description": "Tools for processing Telegram Star Gifts data and generating collection images", "main": "get-collections.mjs", "type": "module", "scripts": {"get-collections": "node get-collections.mjs", "clean-gifts": "node clean-star-gifts.cjs", "generate-images": "node generate-collection-images.cjs", "generate-svg": "node generate-collection-images-svg.cjs", "download-stickers": "node download-sticker-images.mjs", "convert-tgs": "node convert-tgs-to-images.mjs", "generate-from-downloads": "node generate-images-from-downloads.mjs", "process-all": "npm run clean-gifts && npm run generate-svg", "download-all": "node run-download-process.mjs", "full-process": "node run-download-process.mjs"}, "dependencies": {"telegram": "^2.22.2", "canvas": "^2.11.2", "sharp": "^0.33.0", "puppeteer": "^21.0.0"}, "devDependencies": {}, "keywords": ["telegram", "star-gifts", "collections", "image-generation"], "author": "", "license": "MIT"}